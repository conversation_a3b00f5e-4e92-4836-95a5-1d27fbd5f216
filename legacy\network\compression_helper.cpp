#include "compression_helper.h"
#include <QByteArray>
#include <QDebug>

// 启用压缩请求，但暂时禁用解压功能
#define ZLIB_AVAILABLE 1

// 检测zlib可用性
static bool checkZlibAvailability() {
    static bool checked = false;
    static bool available = false;

    if (!checked) {
#if ZLIB_AVAILABLE
        available = true;
#else
        available = false;
#endif
        checked = true;
    }

    return available;
}

bool CompressionHelper::isGzipSupported()
{
    return checkZlibAvailability();
}

QString CompressionHelper::decompressGzip(const QString& gzipData)
{
    // 暂时禁用解压功能，避免编译问题
    // 但仍然支持压缩请求，这样可以测试服务器是否返回压缩数据
    return gzipData;
}

QString CompressionHelper::getAcceptEncoding(bool forLogin)
{
    if (forLogin) {
        // 登录请求不使用压缩
        return "identity";
    } else if (isGzipSupported()) {
        // 支持压缩时请求压缩
        return "gzip, deflate";
    } else {
        // 不支持压缩时不请求压缩
        return "identity";
    }
}
