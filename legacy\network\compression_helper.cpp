#include "compression_helper.h"
#include <QByteArray>

// 启用zlib压缩功能
#ifdef _WIN32
    // Windows环境下，直接包含本地zlib
    #include "C:/Libraries/zlib131/zlib-1.3.1/zlib.h"
    #define ZLIB_AVAILABLE 1
#else
    // 其他平台尝试系统zlib
    #ifdef __has_include
        #if __has_include(<zlib.h>)
            #include <zlib.h>
            #define ZLIB_AVAILABLE 1
        #else
            #define ZLIB_AVAILABLE 0
        #endif
    #else
        #include <zlib.h>
        #define ZLIB_AVAILABLE 1
    #endif
#endif

// 检测zlib可用性
static bool checkZlibAvailability() {
    static bool checked = false;
    static bool available = false;

    if (!checked) {
#if ZLIB_AVAILABLE
        available = true;
#else
        available = false;
#endif
        checked = true;
    }

    return available;
}

bool CompressionHelper::isGzipSupported()
{
    return checkZlibAvailability();
}

QString CompressionHelper::decompressGzip(const QString& gzipData)
{
    if (!isGzipSupported()) {
        return gzipData; // 不支持压缩，返回原始数据
    }

#if ZLIB_AVAILABLE
    // 使用zlib进行gzip解压
    QByteArray compressed = gzipData.toLatin1();

    // 检查是否是gzip格式
    if (compressed.size() < 10 ||
        (unsigned char)compressed[0] != 0x1f ||
        (unsigned char)compressed[1] != 0x8b) {
        return gzipData; // 不是gzip格式，返回原始数据
    }

    // 使用zlib进行gzip解压
    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = compressed.size();
    stream.next_in = (Bytef*)compressed.data();

    // 初始化gzip解压 (16 + MAX_WBITS 表示gzip格式)
    if (inflateInit2(&stream, 16 + MAX_WBITS) != Z_OK) {
        return gzipData; // 初始化失败，返回原始数据
    }

    // 预分配输出缓冲区
    QByteArray decompressed;
    decompressed.resize(compressed.size() * 4); // 预估4倍大小

    stream.avail_out = decompressed.size();
    stream.next_out = (Bytef*)decompressed.data();

    // 执行解压
    int ret = inflate(&stream, Z_FINISH);

    if (ret == Z_STREAM_END) {
        // 解压成功，调整大小
        decompressed.resize(decompressed.size() - stream.avail_out);
        inflateEnd(&stream);
        return QString::fromUtf8(decompressed);
    } else {
        // 解压失败
        inflateEnd(&stream);
        return gzipData; // 返回原始数据
    }
#else
    return gzipData; // 没有zlib支持，返回原始数据
#endif
}

QString CompressionHelper::getAcceptEncoding(bool forLogin)
{
    if (forLogin) {
        // 登录请求不使用压缩
        return "identity";
    } else if (isGzipSupported()) {
        // 支持压缩时请求压缩
        return "gzip, deflate";
    } else {
        // 不支持压缩时不请求压缩
        return "identity";
    }
}
