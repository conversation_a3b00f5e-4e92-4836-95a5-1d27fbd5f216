#include "compression_helper.h"
#include <QByteArray>

// 尝试在运行时检测zlib可用性
static bool checkZlibAvailability() {
    static bool checked = false;
    static bool available = false;
    
    if (!checked) {
        // 这里可以尝试动态加载zlib库或检查系统是否有zlib
        // 目前先返回false，避免编译问题
        available = false;
        checked = true;
    }
    
    return available;
}

bool CompressionHelper::isGzipSupported()
{
    return checkZlibAvailability();
}

QString CompressionHelper::decompressGzip(const QString& gzipData)
{
    if (!isGzipSupported()) {
        return gzipData; // 不支持压缩，返回原始数据
    }
    
    // 这里可以添加实际的解压代码
    // 目前先返回原始数据
    return gzipData;
}

QString CompressionHelper::getAcceptEncoding(bool forLogin)
{
    if (forLogin) {
        // 登录请求不使用压缩
        return "identity";
    } else if (isGzipSupported()) {
        // 支持压缩时请求压缩
        return "gzip, deflate";
    } else {
        // 不支持压缩时不请求压缩
        return "identity";
    }
}
