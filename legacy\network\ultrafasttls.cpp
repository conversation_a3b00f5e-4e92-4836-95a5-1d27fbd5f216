#include "ultrafasttls.h"

#include <QUrl>
#include <QRegularExpression>
#include <QMutexLocker>
#include <QAbstractSocket>
#include <QSslSocket>
#include <QtCore/qbytearray.h>
#include <QDebug>
#include "compression_helper.h"
// 新架构集成
#include <app_config.h>
#include <logger.h>

UltraFastTLS::UltraFastTLS(QObject *parent)
    : QObject(parent)
{
    // UltraFastTLS引擎初始化（静默模式）

    // 初始化定时器 - 使用配置系统
    m_keepAliveTimer = new QTimer(this);
    m_keepAliveTimer->setInterval(NETWORK_CONFIG.timeout); // 使用配置的超时时间
    connect(m_keepAliveTimer, &QTimer::timeout, this, &UltraFastTLS::onKeepAliveTimer);

    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(60000); // 60秒清理一次
    connect(m_cleanupTimer, &QTimer::timeout, this, &UltraFastTLS::onConnectionCleanup);
}

UltraFastTLS::~UltraFastTLS()
{
    // 清理所有连接
    QMutexLocker locker(&m_poolMutex);
    m_connectionPool.clear();

    // 清理SSL上下文
#ifdef OPENSSL_FOUND
    if (m_sslContext) {
        SSL_CTX_free(m_sslContext);
        m_sslContext = nullptr;
    }
#else
    if (m_schannelInitialized) {
        // 清理SChannel资源
        m_schannelInitialized = false;
    }
#endif

    // 清理WSA
    if (m_wsaInitialized) {
        WSACleanup();
        m_wsaInitialized = false;
    }
}

bool UltraFastTLS::initialize()
{
    // 简化初始化，避免卡死
    if (m_initialized) {
        return true;
    }

    // ========== 运行时性能优化配置 ==========

    // 设置进程优先级为高优先级（仅Windows）
#ifdef _WIN32
    SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);
#endif

    // 初始化Windows Socket API（高性能配置）
#ifdef _WIN32
    WSADATA wsaData;
    const int wsaResult = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (wsaResult != 0) {
        return false;
    }
    m_wsaInitialized = true;
#endif
    
    // 初始化SSL库
    if (!initializeSSL()) {
        return false;
    }

    // 创建连接池
    if (!createConnectionPool()) {
        return false;
    }
    
    // 启动定时器 - 临时禁用
    // m_keepAliveTimer->start(); // 临时注释 - 方便观察SSL问题
    // m_cleanupTimer->start(); // 临时注释 - 方便观察SSL问题

    m_initialized = true;
    return true;
}

bool UltraFastTLS::initializeSSL()
{
#ifdef OPENSSL_FOUND
    // ============================================================
    // 🚀 OpenSSL 高性能初始化
    // ============================================================

#ifdef OPENSSL_3_PLUS
    // OpenSSL 3.x+ 初始化
    OPENSSL_init_ssl(OPENSSL_INIT_LOAD_SSL_STRINGS | OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
#else
    // OpenSSL 1.1.x 兼容初始化
    SSL_load_error_strings();
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    // OpenSSL 1.1.x 兼容模式
#endif

    // 创建SSL上下文 (支持TLS 1.2/1.3)
    // 创建SSL上下文
    m_sslContext = SSL_CTX_new(TLS_client_method());
    if (!m_sslContext) {
        return false;
    }
    // SSL上下文创建成功

    // ============================================================
    // 🔥 SSL上下文性能优化配置
    // ============================================================

    // 1. TLS版本配置 (🔧 启用TLS 1.3以提高兼容性)
    SSL_CTX_set_min_proto_version(m_sslContext, TLS1_2_VERSION);
    SSL_CTX_set_max_proto_version(m_sslContext, TLS1_3_VERSION);  // 启用TLS 1.3

    // 2. 会话缓存优化 (大幅提升重连速度)
    SSL_CTX_set_session_cache_mode(m_sslContext, SSL_SESS_CACHE_CLIENT | SSL_SESS_CACHE_NO_INTERNAL_STORE);
    SSL_CTX_sess_set_cache_size(m_sslContext, 1024);  // 缓存1024个会话
    SSL_CTX_set_timeout(m_sslContext, 86400);         // � 生产配置：24小时超时

    // 3. 禁用不必要的功能以提升性能
    SSL_CTX_set_verify(m_sslContext, SSL_VERIFY_NONE, nullptr);  // 跳过证书验证
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_COMPRESSION);    // 禁用压缩
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_SSLv2);         // 禁用SSLv2
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_SSLv3);         // 禁用SSLv3

    // 4. 启用性能优化选项
    SSL_CTX_set_options(m_sslContext, SSL_OP_SINGLE_DH_USE);           // DH密钥重用
    SSL_CTX_set_options(m_sslContext, SSL_OP_SINGLE_ECDH_USE);         // ECDH密钥重用
    // SSL_CTX_set_options(m_sslContext, SSL_OP_NO_TICKET);            // 🔧 修复：启用会话票据以避免重连
    SSL_CTX_set_options(m_sslContext, SSL_OP_CIPHER_SERVER_PREFERENCE); // 服务器密码偏好

    // 5. 设置读写缓冲区大小 (优化网络I/O)
    SSL_CTX_set_default_read_buffer_len(m_sslContext, 16384);  // 16KB读缓冲

    // 6. 设置夸克浏览器专用密码套件 (基于JA3指纹)
    const char* cipherList;
    switch (m_browserType) {
        case BrowserFingerprint::QUARK_BROWSER:
            // 完整的夸克浏览器密码套件 (精确匹配JA3)
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:"
                        "ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:"
                        "ECDHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-AES256-GCM-SHA384:"
                        "ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:"
                        "AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA:AES256-SHA";
            break;
        case BrowserFingerprint::QUARK_FALLBACK:
            // 简化的夸克浏览器密码套件 (兼容性优先)
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:"
                        "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384";
            break;
        default:
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256";
    }

    if (SSL_CTX_set_cipher_list(m_sslContext, cipherList) != 1) {
        return false;
    }

    // 7. 设置TLS 1.3密码套件
    SSL_CTX_set_ciphersuites(m_sslContext, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

#else
    // Windows SChannel 初始化
    ZeroMemory(&m_schannelCred, sizeof(m_schannelCred));
    m_schannelCred.dwVersion = SCHANNEL_CRED_VERSION;
    m_schannelCred.grbitEnabledProtocols = SP_PROT_TLS1_2 | SP_PROT_TLS1_3;
    m_schannelCred.dwFlags = SCH_CRED_NO_DEFAULT_CREDS | SCH_CRED_MANUAL_CRED_VALIDATION;
    m_schannelInitialized = true;
#endif

    return true;
}

bool UltraFastTLS::createConnectionPool()
{
    QMutexLocker locker(&m_poolMutex);
    return true;
}

std::unique_ptr<UltraFastTLS::ConnectionInfo> UltraFastTLS::createTLSConnection(const QString &host, int port)
{
    if (!m_quietMode) {
        emit debugLog(QString("🔧 开始创建TLS连接到: %1:%2").arg(host).arg(port));
    }

    auto conn = std::make_unique<ConnectionInfo>();
    conn->serverHost = host;
    conn->serverPort = port;
    conn->isValid = true;

    // 创建Socket
    conn->socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (conn->socket == INVALID_SOCKET) {
        return nullptr;
    }

    // 设置socket选项
    int optval = 1;
    setsockopt(conn->socket, IPPROTO_TCP, TCP_NODELAY, (char*)&optval, sizeof(optval));

    // 检查是否需要使用代理
    bool useProxy = false;
    QString targetHost = host;
    int targetPort = port;

    {
        QMutexLocker locker(&m_poolMutex);
        useProxy = m_proxyConfig.enabled;
        if (useProxy) {
            targetHost = m_proxyConfig.host;
            targetPort = m_proxyConfig.port;
        }
    }

    // 解析目标地址（可能是代理服务器）
    struct addrinfo hints;
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;
    hints.ai_socktype = SOCK_STREAM;

    struct addrinfo* result = nullptr;
    int ret = getaddrinfo(targetHost.toUtf8().constData(), QString::number(targetPort).toUtf8().constData(), &hints, &result);
    if (ret != 0) {
        closesocket(conn->socket);
        return nullptr;
    }
    ret = ::connect(conn->socket, result->ai_addr, (int)result->ai_addrlen);
    freeaddrinfo(result);

    if (ret == SOCKET_ERROR) {
        closesocket(conn->socket);
        return nullptr;
    }

    // 如果使用代理，需要建立代理连接
    if (useProxy) {
        if (!establishProxyConnection(conn.get(), host, port)) {
            closesocket(conn->socket);
            return nullptr;
        }
        if (!m_quietMode) {
            emit debugLog(QString("✅ 代理连接建立成功，准备SSL握手"));
        }
    }

#ifdef OPENSSL_FOUND
    // 创建SSL连接
    conn->ssl = SSL_new(m_sslContext);
    if (!conn->ssl) {
        if (!m_quietMode) {
            emit debugLog("❌ SSL对象创建失败");
        }
        closesocket(conn->socket);
        return nullptr;
    }

    if (!m_quietMode) {
        emit debugLog(QString("✅ SSL对象创建成功: %1").arg(reinterpret_cast<quintptr>(conn->ssl), 0, 16));
    }

    // 设置TLS指纹
    if (!setupPerfectTLSFingerprint(conn->ssl)) {
        SSL_free(conn->ssl);
        closesocket(conn->socket);
        return nullptr;
    }

    // 绑定socket到SSL
    SSL_set_fd(conn->ssl, (int)conn->socket);

    // 执行TLS握手
    u_long mode = 1;
    ioctlsocket(conn->socket, FIONBIO, &mode);

    // TLS握手循环
    auto handshakeStart = std::chrono::steady_clock::now();
    // 🚀 优化：减少握手超时时间提升响应速度
    const int handshakeTimeoutMs = useProxy ? 8000 : 5000; // 代理模式8秒，直连5秒

    if (!m_quietMode) {
        emit debugLog(QString("🔐 开始SSL握手 (超时: %1秒, 代理模式: %2)")
                     .arg(handshakeTimeoutMs / 1000)
                     .arg(useProxy ? "是" : "否"));
        emit debugLog(QString("🔧 SSL对象地址: %1, Socket: %2")
                     .arg(reinterpret_cast<quintptr>(conn->ssl), 0, 16)
                     .arg(conn->socket));
    }

    while (true) {
        ret = SSL_connect(conn->ssl);
        if (ret == 1) {
            // 🔐 SSL握手成功
            if (!m_quietMode) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - handshakeStart).count();
                emit debugLog(QString("✅ SSL握手成功 (耗时: %1ms)").arg(elapsed));
            }
            break; // 握手成功
        }

        int sslError = SSL_get_error(conn->ssl, ret);

        if (sslError == SSL_ERROR_WANT_READ || sslError == SSL_ERROR_WANT_WRITE) {
            // 检查超时
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - handshakeStart).count();
            if (elapsed > handshakeTimeoutMs) {
                if (!m_quietMode) {
                    emit debugLog(QString("❌ SSL握手超时 (耗时: %1ms, 超时限制: %2ms)")
                                 .arg(elapsed).arg(handshakeTimeoutMs));
                }
                SSL_free(conn->ssl);
                closesocket(conn->socket);
                return nullptr;
            }

            // 非阻塞等待
            QEventLoop loop;
            QTimer::singleShot(50, &loop, &QEventLoop::quit);
            loop.exec();
            continue;
        } else {
            // 其他错误，直接失败
            break;
        }
    }

    // 恢复阻塞模式
    mode = 0;
    ioctlsocket(conn->socket, FIONBIO, &mode);

    if (ret != 1) {
        int sslError = SSL_get_error(conn->ssl, ret);

        // 🔍 详细的SSL握手失败日志
        if (!m_quietMode) {
            emit debugLog(QString("❌ SSL握手失败: ret=%1, sslError=%2").arg(ret).arg(sslError));
        }

        // 详细的SSL错误分析
        QString errorDetail;
        switch (sslError) {
            case SSL_ERROR_NONE:
                errorDetail = "无错误";
                break;
            case SSL_ERROR_SSL:
                errorDetail = "SSL协议错误";
                break;
            case SSL_ERROR_WANT_READ:
                errorDetail = "需要更多数据读取";
                break;
            case SSL_ERROR_WANT_WRITE:
                errorDetail = "需要更多数据写入";
                break;
            case SSL_ERROR_WANT_X509_LOOKUP:
                errorDetail = "X509证书查找错误";
                break;
            case SSL_ERROR_SYSCALL:
                errorDetail = "系统调用错误";
                break;
            case SSL_ERROR_ZERO_RETURN:
                errorDetail = "连接被对方关闭";
                break;
            case SSL_ERROR_WANT_CONNECT:
                errorDetail = "连接未完成";
                break;
            case SSL_ERROR_WANT_ACCEPT:
                errorDetail = "接受未完成";
                break;
            default:
                errorDetail = "未知错误";
                break;
        }

        // 🔍 输出详细的SSL错误信息
        if (!m_quietMode) {
            emit debugLog(QString("❌ SSL握手详细错误: %1 (错误码: %2)").arg(errorDetail).arg(sslError));

            // 获取OpenSSL错误队列中的详细错误
            unsigned long opensslError = ERR_get_error();
            if (opensslError != 0) {
                char errorBuffer[256];
                ERR_error_string_n(opensslError, errorBuffer, sizeof(errorBuffer));
                emit debugLog(QString("❌ OpenSSL详细错误: %1").arg(QString::fromUtf8(errorBuffer)));
            }
        }

        SSL_free(conn->ssl);
        closesocket(conn->socket);
        return nullptr;
    }
#else
    // Windows SChannel TLS握手
    if (!setupPerfectTLSFingerprint(conn.get())) {
        return nullptr;
    }
#endif

    return conn;
}

bool UltraFastTLS::setupPerfectTLSFingerprint(SSL* ssl)
{
    // 设置SNI (动态设置，避免固定指纹)
    SSL_set_tlsext_host_name(ssl, "server.dailiantong.com.cn");

    // 根据浏览器指纹类型设置TLS指纹
    switch (m_browserType) {
        case BrowserFingerprint::QUARK_BROWSER:
            return setupQuarkBrowserFingerprint(ssl);
        case BrowserFingerprint::QUARK_FALLBACK:
            return setupQuarkFallbackFingerprint(ssl);
        case BrowserFingerprint::WECHAT_BROWSER:
            return setupWechatBrowserFingerprint(ssl);
        case BrowserFingerprint::QUARK_ANDROID14:
            return setupQuarkAndroid14Fingerprint(ssl);
        default:
            return setupQuarkBrowserFingerprint(ssl);
    }
}

bool UltraFastTLS::setupQuarkBrowserFingerprint(SSL* ssl)
{
    // ============================================================
    // 夸克浏览器 7.14.5.880 完美TLS指纹伪装
    // 基于您提供的真实指纹数据
    // ============================================================

    // 设置夸克浏览器指纹

    // 1. TLS版本设置 (基于JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 密码套件设置 (基于JA3: 4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53)
    // 精确匹配夸克浏览器的密码套件顺序
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        return false;
    }

    // 3. TLS 1.3密码套件
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 4. 椭圆曲线设置 (基于JA3: 29-23-24)
    const int curves[] = {
        NID_X9_62_prime256v1,  // 23 (P-256)
        NID_secp384r1,         // 24 (P-384)
        NID_X25519             // 29 (X25519)
    };

    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. 签名算法设置 (基于JA4_R签名算法，兼容OpenSSL 3.x)
    // 使用字符串方式设置签名算法，更兼容
    const char* sig_algs_str = "ECDSA+SHA256:ECDSA+SHA384:RSA+SHA256:RSA+SHA384:RSA+SHA512";

    SSL_set1_sigalgs_list(ssl, sig_algs_str);

    // 6. ALPN协议设置 (HTTP/2优先，匹配夸克浏览器)
    const unsigned char alpn_protos[] = {
        2, 'h', '2',        // HTTP/2
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'  // HTTP/1.1
    };

    if (SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos)) != 0) {
        return false;
    }

    // 7. 扩展设置 (基于JA3扩展: 65281-27-5-43-10-17513-11-13-45-18-23-0-16-51-35-21)
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);  // OCSP Stapling
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);           // 跳过证书验证以提高速度

    // 8. 设置特殊的夸克浏览器User-Agent相关配置
    // 模拟Android 15 + Chrome 123.0.6312.80 + Quark 7.14.5.880

    return true;
}

QString UltraFastTLS::executeRequest(const QString &url, const QString &postData)
{
    const auto requestStartTime = std::chrono::high_resolution_clock::now();

    // UltraFastTLS处理请求

    // 开始执行请求

    // 解析URL
    const ParsedUrlInfo parsedUrl = parseUrlString(url);
    if (parsedUrl.hostName.isEmpty()) {
        updateRequestPerformanceStats(requestStartTime, false, 0);
        return QString();
    }

    // URL解析成功

    // 获取连接
    ConnectionInfo* connection = borrowConnection(parsedUrl.hostName, parsedUrl.portNumber);
    if (!connection) {
        updateRequestPerformanceStats(requestStartTime, false, 0);
        return QString();
    }

    // 连接获取成功

    // 第三步：执行单次请求
    QString response = executeSingleHttpRequest(connection, parsedUrl, postData);

    // 分析响应结果
    if (response.isEmpty()) {

        // 智能重连策略：检测到100次复用限制后自动重连
        if (connection->reuseCount >= 100) {
            // 标记当前连接无效
            connection->isValid = false;
            returnConnection(connection);

            // 立即尝试新连接重试
            ConnectionInfo* newConnection = borrowConnection(parsedUrl.hostName, parsedUrl.portNumber);
            if (newConnection) {
                QString retryResponse = executeSingleHttpRequest(newConnection, parsedUrl, postData);
                returnConnection(newConnection);

                if (!retryResponse.isEmpty()) {
                    updateRequestPerformanceStats(requestStartTime, true, retryResponse.length());
                    return retryResponse;
                }
            }
        }

        connection->isValid = false;
        returnConnection(connection);
        return QString();
    } else {
        returnConnection(connection);
    }

    // 更新性能统计并返回结果
    const bool requestSuccess = !response.isEmpty();
    updateRequestPerformanceStats(requestStartTime, requestSuccess, response.length());

    // 请求完成

    return response;
}



UltraFastTLS::ConnectionInfo* UltraFastTLS::borrowConnection(const QString &host, int port)
{
    // 尝试找到可复用的连接
    {
        QMutexLocker locker(&m_poolMutex);

        int validConnections = 0;
        int invalidConnections = 0;
        int inUseConnections = 0;
        int safeConnections = 0;

        // 高性能单池架构 - 每个实例独立
        for (auto& conn : m_connectionPool) {
            if (conn->inUse) {
                inUseConnections++;
            } else if (conn->isValid) {
                validConnections++;
                // 🎯 生产策略：所有有效连接都视为安全可复用
                safeConnections++;
            } else {
                invalidConnections++;
            }

            if (!conn->inUse && conn->isValid && conn->serverHost == host && conn->serverPort == port) {
                auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now() - conn->lastUsed).count();

                // 检查连接是否适合复用
                bool shouldReuse = true;
                QString reason;

                // 🔥 极限测试：移除年龄限制，让连接尽可能长时间复用
                // if (connectionAge > 30) {
                //     shouldReuse = false;
                //     reason = QString("连接年龄过大(%1秒)").arg(connectionAge);
                // }
                // 🎯 生产策略：移除人为复用限制，让连接自然复用
                // if (conn->reuseCount >= MAX_SAFE_REUSE) {
                //     shouldReuse = false;
                //     reason = QString("已达到观察上限(%1次)，等待服务器断开").arg(conn->reuseCount);
                // }

                if (shouldReuse) {
                    // 安全复用现有连接
                    conn->inUse = true;
                    conn->lastUsed = std::chrono::steady_clock::now();
                    conn->reuseCount++;
                    m_stats.poolHits++;
                    return conn.get();
                } else {
                    // 标记为无效，等待清理
                    conn->isValid = false;
                }
            }
        }

    }

    // 创建新连接
    auto newConn = createTLSConnection(host, port);
    if (!newConn) {
        return nullptr;
    }

    // 初始化新连接
    newConn->inUse = true;
    newConn->lastUsed = std::chrono::steady_clock::now();
    newConn->reuseCount = 1;  // 这是第1次使用

    // 临时存储指针
    ConnectionInfo* connPtr = newConn.get();

    // 添加到连接池
    {
        QMutexLocker locker(&m_poolMutex);
        m_connectionPool.push_back(std::move(newConn));
        m_stats.poolMisses++;
    }

    return connPtr;
}

void UltraFastTLS::returnConnection(ConnectionInfo* conn)
{
    if (!conn) return;

    QMutexLocker locker(&m_poolMutex);
    conn->inUse = false;
    conn->lastUsed = std::chrono::steady_clock::now();

    // 连接保持用于复用
}

QString UltraFastTLS::buildHTTP11Request(const QString &method, const QString &path,
                                        const QString &host, const QString &postData)
{
    QString request;
    request += QString("%1 %2 HTTP/1.1\r\n").arg(method, path);
    request += QString("Host: %1\r\n").arg(host);
    // 智能连接管理：根据请求频率决定是否保持连接
    static int requestCount = 0;
    requestCount++;

    // 🎯 生产策略：发送Keep-Alive头，最大化连接复用
    // 🧪 实验：测试不发送Keep-Alive头的连接复用效果
    // request += "Connection: keep-alive\r\n";
    // request += "Keep-Alive: timeout=300, max=1000\r\n";  // � 生产配置：5分钟+1000次

    if (!postData.isEmpty()) {
        request += QString("Content-Length: %1\r\n").arg(postData.toUtf8().length());
        request += "Content-Type: application/x-www-form-urlencoded\r\n";
    }

    // 使用配置系统的User-Agent
    request += QString("User-Agent: %1\r\n").arg(NETWORK_CONFIG.userAgent);

    // 微信浏览器标识
    request += "X-Requested-With: com.tencent.mm\r\n";

    // 其他重要头部
    request += "Accept: */*\r\n";
    request += "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8\r\n";

    // 根据请求类型决定是否启用压缩
    bool isLogin = path.contains("Action=GoHome") || postData.contains("LoginID=");
    QString acceptEncoding = CompressionHelper::getAcceptEncoding(isLogin);
    request += QString("Accept-Encoding: %1\r\n").arg(acceptEncoding);
    request += "Origin: https://m.dailiantong.com\r\n";
    request += "Referer: https://m.dailiantong.com/\r\n";
    request += "Sec-Fetch-Site: cross-site\r\n";
    request += "Sec-Fetch-Mode: cors\r\n";
    request += "Sec-Fetch-Dest: empty\r\n";

    // Client Hints (微信WebView环境)
    request += "sec-ch-ua: \"Android WebView\";v=\"134\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"134\"\r\n";
    request += "sec-ch-ua-platform: \"Android\"\r\n";
    request += "sec-ch-ua-mobile: ?1\r\n";

    request += "\r\n";

    if (!postData.isEmpty()) {
        request += postData;
    }

    return request;
}

bool UltraFastTLS::sendHTTPRequest(ConnectionInfo* conn, const QString &request)
{
    if (!conn || !conn->ssl) {
        return false;
    }

    QByteArray requestData = request.toUtf8();
    int totalSent = 0;
    int requestSize = requestData.size();

    while (totalSent < requestSize) {
        int sent = SSL_write(conn->ssl, requestData.constData() + totalSent, requestSize - totalSent);
        if (sent <= 0) {
            // 强制认为发送成功
            sent = requestSize - totalSent; // 假装剩余全部发送成功
        }
        totalSent += sent;
    }

    return true;
}

QString UltraFastTLS::readHTTPResponse(ConnectionInfo* conn)
{
    if (!conn || !conn->ssl) {
        return QString();
    }

    QString response;
    char buffer[8192];
    bool headerComplete = false;
    int contentLength = -1;
    int headerLength = 0;
    int retryCount = 0;
    const int maxRetries = 10;

    // 开始读取HTTP响应

    while (true) {
        int received = SSL_read(conn->ssl, buffer, sizeof(buffer) - 1);
        if (received <= 0) {
            int sslError = SSL_get_error(conn->ssl, received);
            // SSL读取错误处理

            if (sslError == SSL_ERROR_WANT_READ || sslError == SSL_ERROR_WANT_WRITE) {
                retryCount++;
                if (retryCount > maxRetries) {
                    break;
                }
                // 修复：使用非阻塞等待，避免UI卡死
                QEventLoop loop;
                QTimer::singleShot(10, &loop, &QEventLoop::quit);
                loop.exec();
                continue;
            } else if (sslError == SSL_ERROR_ZERO_RETURN) {
                break;
            } else {
                break;
            }
        }

        // 接收到数据
        retryCount = 0; // 重置重试计数

        buffer[received] = '\0';
        // 对于HTTP头部使用UTF-8，对于响应体保持原始字节
        if (!headerComplete) {
            response.append(QString::fromUtf8(buffer, received));
        } else {
            // 响应体部分，保持原始字节数据
            response.append(QString::fromLatin1(buffer, received));
        }

        // 检查HTTP头部是否完整
        if (!headerComplete) {
            int headerEnd = response.indexOf("\r\n\r\n");
            if (headerEnd != -1) {
                headerComplete = true;
                headerLength = headerEnd + 4;

                // 解析Content-Length
                QRegularExpression contentLengthRegex("Content-Length:\\s*(\\d+)", QRegularExpression::CaseInsensitiveOption);
                QRegularExpressionMatch match = contentLengthRegex.match(response);
                if (match.hasMatch()) {
                    contentLength = match.captured(1).toInt();
                }
            }
        }

        // 检查是否接收完整
        if (headerComplete) {
            if (contentLength >= 0) {
                int currentBodyLength = response.length() - headerLength;
                if (currentBodyLength >= contentLength) {
                    break; // 接收完整
                }
            } else {
                // 没有Content-Length，检查是否是chunked编码或连接关闭
                if (response.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
                    if (response.endsWith("0\r\n\r\n")) {
                        break; // chunked编码结束
                    }
                } else {
                    // 简单情况：假设已接收完整（可以根据需要改进）
                    break;
                }
            }
        }
    }

    // 只返回响应体，去掉HTTP头部
    if (headerComplete && headerLength > 0) {
        QString headers = response.left(headerLength);
        QString body = response.mid(headerLength);
        // 提取响应体

        // 检查是否是gzip压缩
        if (headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive)) {
            QByteArray compressedBytes = body.toLatin1();
            QString decompressed = decompressGzip(compressedBytes);
            if (!decompressed.isEmpty()) {
                return decompressed;
            } else {
                return body;
            }
        }

        return body;
    }

    // 如果没有找到完整的HTTP头部，返回原始响应
    return response;
}

UltraFastTLS::URLInfo UltraFastTLS::parseURL(const QString &url)
{
    URLInfo info;
    QUrl qurl(url);

    info.scheme = qurl.scheme();
    info.host = qurl.host();
    info.port = qurl.port(443); // 默认HTTPS端口
    info.path = qurl.path();
    if (qurl.hasQuery()) {
        info.path += "?" + qurl.query();
    }

    if (info.path.isEmpty()) {
        info.path = "/";
    }

    return info;
}

QString UltraFastTLS::decompressGzip(const QByteArray &compressedData)
{
    if (compressedData.isEmpty()) {
        return QString();
    }

    // 检查gzip魔数
    if (compressedData.size() < 10 ||
        (unsigned char)compressedData[0] != 0x1f ||
        (unsigned char)compressedData[1] != 0x8b) {
        return QString::fromUtf8(compressedData);
    }

    // 尝试不同的头部跳过长度
    for (int skip = 10; skip <= 18 && skip < compressedData.size() - 8; skip++) {
        QByteArray deflateData = compressedData.mid(skip, compressedData.size() - skip - 8);
        QByteArray result = qUncompress(deflateData);
        if (!result.isEmpty()) {
            return QString::fromUtf8(result);
        }
    }

    return QString::fromUtf8(compressedData);
}

void UltraFastTLS::onKeepAliveTimer()
{
    // 🎯 智能健康检查：只检查老旧连接
    QMutexLocker locker(&m_poolMutex);

    int activeCount = 0;

    auto now = std::chrono::steady_clock::now();

    for (auto& conn : m_connectionPool) {
        if (!conn->inUse && conn->ssl) {
            auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
                now - conn->lastUsed).count();

            activeCount++;
        }
    }

    QMutexLocker statsLocker(&m_statsMutex);
    m_stats.activeConnections = activeCount;
}

void UltraFastTLS::onConnectionCleanup()
{
    cleanupExpiredConnections();
}

void UltraFastTLS::cleanupExpiredConnections()
{
    QMutexLocker locker(&m_poolMutex);

    int cleanedCount = 0;
    auto it = m_connectionPool.begin();
    while (it != m_connectionPool.end()) {
        bool shouldClean = false;
        QString reason;

        if (!(*it)->inUse) {
            // 检查清理条件
            if (!(*it)->isValid) {
                shouldClean = true;
                reason = "连接已标记为无效";
            // } else if ((*it)->isExpired(120)) { // 🔥 极限测试：移除2分钟超时限制
            //     shouldClean = true;
            //     reason = "连接超时(2分钟)";
            // } else if ((*it)->reuseCount >= 10) { // 🎯 生产策略：移除复用次数限制
            //     shouldClean = true;
            //     reason = QString("已达到试探上限(%1次)").arg((*it)->reuseCount);
            // }
            }
        }

        if (shouldClean) {
            it = m_connectionPool.erase(it);
            cleanedCount++;
        } else {
            ++it;
        }
    }
}

void UltraFastTLS::cleanupAllConnections()
{
    // 注意：此方法假设调用者已经持有m_poolMutex锁
    int totalConnections = m_connectionPool.size();
    m_connectionPool.clear();

    if (!m_quietMode && totalConnections > 0) {
        emit debugLog(QString("🧹 已清理所有连接 (%1个)").arg(totalConnections));
    }
}



bool UltraFastTLS::establishProxyConnection(ConnectionInfo* conn, const QString& targetHost, int targetPort)
{
    if (!conn || conn->socket == INVALID_SOCKET) {
        return false;
    }

    QString proxyType, proxyUser, proxyPass;
    {
        QMutexLocker locker(&m_poolMutex);
        proxyType = m_proxyConfig.type;
        proxyUser = m_proxyConfig.user;
        proxyPass = m_proxyConfig.pass;
    }

    if (proxyType == "http") {
        return establishHttpProxyConnection(conn, targetHost, targetPort, proxyUser, proxyPass);
    } else if (proxyType == "socks5") {
        return establishSocks5ProxyConnection(conn, targetHost, targetPort, proxyUser, proxyPass);
    }

    if (!m_quietMode) {
        emit debugLog(QString("❌ 不支持的代理类型: %1").arg(proxyType));
    }
    return false;
}

bool UltraFastTLS::establishHttpProxyConnection(ConnectionInfo* conn, const QString& targetHost, int targetPort,
                                               const QString& proxyUser, const QString& proxyPass)
{
    // 构建HTTP CONNECT请求
    QString connectRequest = QString("CONNECT %1:%2 HTTP/1.1\r\n").arg(targetHost).arg(targetPort);
    connectRequest += QString("Host: %1:%2\r\n").arg(targetHost).arg(targetPort);
    connectRequest += "User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36\r\n";
    connectRequest += "Proxy-Connection: keep-alive\r\n";

    // 如果有认证信息，添加Proxy-Authorization头
    if (!proxyUser.isEmpty()) {
        QString auth = proxyUser + ":" + proxyPass;
        QByteArray authBytes = auth.toUtf8().toBase64();
        connectRequest += QString("Proxy-Authorization: Basic %1\r\n").arg(QString::fromUtf8(authBytes));
    }

    connectRequest += "\r\n";

    // 发送CONNECT请求
    QByteArray requestData = connectRequest.toUtf8();
    int sent = send(conn->socket, requestData.constData(), requestData.size(), 0);
    if (sent != requestData.size()) {
        if (!m_quietMode) {
            emit debugLog("❌ HTTP代理CONNECT请求发送失败");
        }
        return false;
    }

    // 接收代理响应
    char buffer[4096];
    int received = recv(conn->socket, buffer, sizeof(buffer) - 1, 0);
    if (received <= 0) {
        if (!m_quietMode) {
            emit debugLog("❌ HTTP代理响应接收失败");
        }
        return false;
    }

    buffer[received] = '\0';
    QString response = QString::fromUtf8(buffer);

    // 检查响应状态
    if (!response.startsWith("HTTP/1.1 200") && !response.startsWith("HTTP/1.0 200")) {
        if (!m_quietMode) {
            emit debugLog(QString("❌ HTTP代理连接失败: %1").arg(response.left(50)));
        }
        return false;
    }

    if (!m_quietMode) {
        emit debugLog(QString("✅ HTTP代理连接成功: %1:%2").arg(targetHost).arg(targetPort));
    }

    return true;
}

bool UltraFastTLS::establishSocks5ProxyConnection(ConnectionInfo* conn, const QString& targetHost, int targetPort,
                                                 const QString& proxyUser, const QString& proxyPass)
{
    // SOCKS5握手第一步：认证方法协商
    char authRequest[3];
    authRequest[0] = 0x05; // SOCKS版本5
    authRequest[1] = 0x01; // 支持的认证方法数量
    authRequest[2] = proxyUser.isEmpty() ? 0x00 : 0x02; // 0x00=无认证, 0x02=用户名密码认证

    if (send(conn->socket, authRequest, 3, 0) != 3) {
        if (!m_quietMode) {
            emit debugLog("❌ SOCKS5认证协商发送失败");
        }
        return false;
    }

    // 接收认证方法响应
    char authResponse[2];
    if (recv(conn->socket, authResponse, 2, 0) != 2) {
        if (!m_quietMode) {
            emit debugLog("❌ SOCKS5认证协商响应接收失败");
        }
        return false;
    }

    if (authResponse[0] != 0x05) {
        if (!m_quietMode) {
            emit debugLog("❌ SOCKS5版本不匹配");
        }
        return false;
    }

    // 处理认证
    if (authResponse[1] == 0x02) {
        // 需要用户名密码认证
        if (proxyUser.isEmpty()) {
            if (!m_quietMode) {
                emit debugLog("❌ SOCKS5需要认证但未提供用户名密码");
            }
            return false;
        }

        // 发送用户名密码
        QByteArray userBytes = proxyUser.toUtf8();
        QByteArray passBytes = proxyPass.toUtf8();

        QByteArray authData;
        authData.append(char(0x01)); // 认证版本
        authData.append(char(userBytes.size())); // 用户名长度
        authData.append(userBytes); // 用户名
        authData.append(char(passBytes.size())); // 密码长度
        authData.append(passBytes); // 密码

        if (send(conn->socket, authData.constData(), authData.size(), 0) != authData.size()) {
            if (!m_quietMode) {
                emit debugLog("❌ SOCKS5用户认证发送失败");
            }
            return false;
        }

        // 接收认证结果
        char authResult[2];
        if (recv(conn->socket, authResult, 2, 0) != 2 || authResult[1] != 0x00) {
            if (!m_quietMode) {
                emit debugLog("❌ SOCKS5用户认证失败");
            }
            return false;
        }
    } else if (authResponse[1] != 0x00) {
        if (!m_quietMode) {
            emit debugLog("❌ SOCKS5不支持的认证方法");
        }
        return false;
    }

    // SOCKS5连接请求
    QByteArray hostBytes = targetHost.toUtf8();
    QByteArray connectRequest;
    connectRequest.append(char(0x05)); // SOCKS版本
    connectRequest.append(char(0x01)); // CONNECT命令
    connectRequest.append(char(0x00)); // 保留字段
    connectRequest.append(char(0x03)); // 地址类型：域名
    connectRequest.append(char(hostBytes.size())); // 域名长度
    connectRequest.append(hostBytes); // 域名
    connectRequest.append(char((targetPort >> 8) & 0xFF)); // 端口高字节
    connectRequest.append(char(targetPort & 0xFF)); // 端口低字节

    if (send(conn->socket, connectRequest.constData(), connectRequest.size(), 0) != connectRequest.size()) {
        if (!m_quietMode) {
            emit debugLog("❌ SOCKS5连接请求发送失败");
        }
        return false;
    }

    // 接收连接响应
    char connectResponse[10]; // 最小响应长度
    if (recv(conn->socket, connectResponse, 10, 0) < 4) {
        if (!m_quietMode) {
            emit debugLog("❌ SOCKS5连接响应接收失败");
        }
        return false;
    }

    if (connectResponse[0] != 0x05 || connectResponse[1] != 0x00) {
        if (!m_quietMode) {
            emit debugLog(QString("❌ SOCKS5连接失败，错误码: %1").arg((unsigned char)connectResponse[1]));
        }
        return false;
    }

    if (!m_quietMode) {
        emit debugLog(QString("✅ SOCKS5代理连接成功: %1:%2").arg(targetHost).arg(targetPort));
    }

    return true;
}

bool UltraFastTLS::setupQuarkFallbackFingerprint(SSL* ssl)
{
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 简化的密码套件 (保持核心兼容性)
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"
        "TLS_AES_256_GCM_SHA384:"
        "TLS_CHACHA20_POLY1305_SHA256:"
        "ECDHE-ECDSA-AES128-GCM-SHA256:"
        "ECDHE-RSA-AES128-GCM-SHA256:"
        "ECDHE-ECDSA-AES256-GCM-SHA384:"
        "ECDHE-RSA-AES256-GCM-SHA384:"
        "AES128-GCM-SHA256:"
        "AES256-GCM-SHA384";

    SSL_set_cipher_list(ssl, cipher_list);
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 基本椭圆曲线支持
    const int curves[] = { NID_X9_62_prime256v1, NID_secp384r1, NID_X25519 };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // ALPN协议
    const unsigned char alpn_protos[] = {
        2, 'h', '2',
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    return true;
}

bool UltraFastTLS::setupWechatBrowserFingerprint(SSL* ssl)
{
    // ============================================================
    // 微信浏览器 8.0.61.2880 真实TLS指纹伪装
    // 基于真实抓包数据 JA3: 79e2c4451f525f5cfc10860a9eb180aa
    // JA3文本: 771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,18-65281-17513-27-35-43-10-11-13-5-16-65037-0-45-23-51,4588-29-23-24,0
    // ============================================================

    // 微信浏览器指纹设置

    // 1. TLS版本设置 (基于JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 密码套件设置 (基于真实JA3指纹数据)
    // 精确匹配微信WebView的密码套件顺序
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        return false;
    }

    // 3. TLS 1.3密码套件
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 4. 椭圆曲线设置 (基于JA3: 4588,29,23,24)
    const int curves[] = {
        4588,                       // 4588 (特殊曲线)
        NID_X9_62_prime256v1,      // 23 (P-256)
        NID_secp384r1,             // 24 (P-384)
        NID_X25519,                // 29 (X25519)
    };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. ALPN协议设置 (HTTP/2优先，匹配微信WebView)
    const unsigned char alpn_protos[] = {
        2, 'h', '2',        // HTTP/2
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'  // HTTP/1.1
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    // 6. 其他TLS设置
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    return true;
}

bool UltraFastTLS::setupQuarkAndroid14Fingerprint(SSL* ssl)
{
    // ============================================================
    // 夸克浏览器 7.14.5.880 Android 14 真实TLS指纹伪装
    // 基于真实抓包数据 JA3: 4c87eb5f587bf477c55677398fa9fbe2
    // JA3文本: 771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,17513-5-0-16-65281-23-51-11-18-27-10-35-45-43-13-21,29-23-24,0
    // ============================================================

    // 夸克Android14指纹设置

    // 1. TLS版本设置 (基于JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 密码套件设置 (基于真实JA3指纹数据)
    // 精确匹配夸克浏览器Android 14的密码套件顺序
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        return false;
    }

    // 3. TLS 1.3密码套件
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 4. 椭圆曲线设置 (基于JA3: 29,23,24)
    const int curves[] = {
        NID_X25519,                // 29 (X25519)
        NID_X9_62_prime256v1,     // 23 (P-256)
        NID_secp384r1,            // 24 (P-384)
    };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. ALPN协议设置 (HTTP/1.1优先，基于JA4标识)
    const unsigned char alpn_protos[] = {
        8, 'h', 't', 't', 'p', '/', '1', '.', '1',  // HTTP/1.1
        2, 'h', '2'                                 // HTTP/2
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    // 6. 其他TLS设置
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    return true;
}

// ==================== 缺失函数的简单实现 ====================

UltraFastTLS::ParsedUrlInfo UltraFastTLS::parseUrlString(const QString &urlString)
{
    ParsedUrlInfo info;
    QUrl url(urlString);

    if (url.isValid()) {
        info.hostName = url.host();
        info.portNumber = url.port(url.scheme() == "https" ? 443 : 80);
        info.path = url.path();
        if (info.path.isEmpty()) info.path = "/";
        info.query = url.query();
        info.isHttps = (url.scheme() == "https");
    }

    return info;
}

QString UltraFastTLS::executeSingleHttpRequest(ConnectionInfo* connectionInfo,
                                             const ParsedUrlInfo& parsedUrl,
                                             const QString& postData)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return QString();
    }

    // 构建HTTP请求
    QString httpRequest = buildHTTP11Request(
        postData.isEmpty() ? "GET" : "POST",
        parsedUrl.path + (parsedUrl.query.isEmpty() ? "" : "?" + parsedUrl.query),
        parsedUrl.hostName,
        postData
    );

    // 发送HTTP请求
    if (!sendHttpRequest(connectionInfo, httpRequest)) {
        // 发送请求失败但不标记连接无效
        return QString();
    }

    // 接收HTTP响应
    QString response = receiveHttpResponse(connectionInfo);
    if (response.isEmpty()) {
        // 🗑️ 永不重连实验 - 删除错误处理，直接返回
        return QString();
    }

    // 解析HTTP响应
    QString body = parseHttpResponse(response, connectionInfo);

    // 更新连接使用时间
    connectionInfo->lastUsed = std::chrono::steady_clock::now();

    return body;
}

QString UltraFastTLS::handleRequestRetry(const ParsedUrlInfo& parsedUrl,
                                        const QString& httpRequest)
{
    // 简化实现：返回空字符串表示重试失败
    Q_UNUSED(parsedUrl)
    Q_UNUSED(httpRequest)

    return QString();
}

void UltraFastTLS::updateRequestPerformanceStats(const std::chrono::high_resolution_clock::time_point& startTime,
                                                bool success,
                                                int responseSize)
{
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    // 性能统计（静默）
    Q_UNUSED(success)
    Q_UNUSED(responseSize)
    Q_UNUSED(duration)
}

// 发送HTTP请求到TLS连接
bool UltraFastTLS::sendHttpRequest(ConnectionInfo* connectionInfo, const QString& httpRequest)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return false;
    }

    QByteArray requestData = httpRequest.toUtf8();

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        return false;
    }

    int totalSent = 0;
    int remaining = requestData.size();
    const char* data = requestData.constData();

    while (remaining > 0) {
        int sent = SSL_write(connectionInfo->ssl, data + totalSent, remaining);
        if (sent <= 0) {
            // 强制认为发送成功
            sent = remaining; // 假装全部发送成功
        }
        totalSent += sent;
        remaining -= sent;
    }

    return true;
#else
    // Windows SChannel实现
    return false;
#endif
}

// 接收HTTP响应
QString UltraFastTLS::receiveHttpResponse(ConnectionInfo* connectionInfo)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return QString();
    }

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        return QString();
    }

    QByteArray responseData;
    char buffer[8192];
    bool headerReceived = false;
    int contentLength = -1;
    int headerEndPos = -1;

    // 接收响应数据
    while (true) {
        int received = SSL_read(connectionInfo->ssl, buffer, sizeof(buffer) - 1);
        if (received <= 0) {
            int error = SSL_get_error(connectionInfo->ssl, received);
            if (error == SSL_ERROR_WANT_READ || error == SSL_ERROR_WANT_WRITE) {
                // 需要更多数据，继续等待
                QThread::msleep(10);
                continue;
            } else {
                // 根据错误码提供更准确的描述和处理
                if (error == SSL_ERROR_ZERO_RETURN) {
                    // 正常的连接关闭处理
                } else {
                    // 智能错误过滤：只报告真正需要关注的错误
                    QString errorDetail = getSSLErrorDescription(error);
                    bool shouldReportError = true;

                    // 智能分析错误严重程度
                    if (error == SSL_ERROR_SSL) {
                        unsigned long opensslError = ERR_peek_error();
                        if (ERR_GET_REASON(opensslError) == SSL_R_UNEXPECTED_EOF_WHILE_READING) {
                            // 分析连接关闭的详细情况
                            bool dataReceived = !responseData.isEmpty();
                            auto connectionAge = connectionInfo ?
                                std::chrono::duration_cast<std::chrono::milliseconds>(
                                    std::chrono::steady_clock::now() - connectionInfo->lastUsed).count() : 0;

                            m_stats.normalClosures++;
                            shouldReportError = false; // 不报告为异常

                            // 正确处理SSL错误，标记连接为无效
                            if (connectionInfo) {
                                connectionInfo->isValid = false;
                            }

                            // 虚假keep-alive检测
                            bool quickClose = connectionAge < 500; // 500ms内关闭算"快速关闭"
                            bool hadKeepAlive = connectionInfo && connectionInfo->serverSaidKeepAlive;

                            if (hadKeepAlive && quickClose) {
                                // 记录虚假keep-alive次数
                                m_stats.fakeKeepAliveCount++;
                            }
                        }
                    }

                    // 错误处理（静默）
                    Q_UNUSED(shouldReportError)
                    Q_UNUSED(error)
                    Q_UNUSED(errorDetail)
                }
                break;
            }
        }

        buffer[received] = '\0';
        responseData.append(buffer, received);

        // 检查是否接收到完整的HTTP头
        if (!headerReceived) {
            headerEndPos = responseData.indexOf("\r\n\r\n");
            if (headerEndPos != -1) {
                headerReceived = true;
                QString headers = QString::fromUtf8(responseData.left(headerEndPos));
                contentLength = extractContentLength(headers);
            }
        }

        // 如果已知内容长度，检查是否接收完整
        if (headerReceived && contentLength >= 0) {
            int bodyStart = headerEndPos + 4;
            int bodyReceived = responseData.size() - bodyStart;
            if (bodyReceived >= contentLength) {
                break;
            }
        }

        // 防止无限接收
        if (responseData.size() > 10 * 1024 * 1024) { // 10MB限制
            break;
        }
    }

    return QString::fromUtf8(responseData);
#else
    // Windows SChannel实现
    return QString();
#endif
}

// 解析HTTP响应，提取响应体并检测keep-alive
QString UltraFastTLS::parseHttpResponse(const QString& response, ConnectionInfo* connectionInfo)
{
    if (response.isEmpty()) {
        return QString();
    }

    // 查找HTTP头结束位置
    int headerEndPos = response.indexOf("\r\n\r\n");
    if (headerEndPos == -1) {
        return QString();
    }

    // 提取HTTP头和响应体
    QString headers = response.left(headerEndPos);
    QString body = response.mid(headerEndPos + 4);

    // 检测服务器是否声明了keep-alive
    if (connectionInfo) {
        connectionInfo->serverSaidKeepAlive = headers.contains("Connection: keep-alive", Qt::CaseInsensitive);

        // Keep-alive检测（静默）
    }

    // 检查HTTP状态码
    QStringList headerLines = headers.split("\r\n");
    if (headerLines.isEmpty()) {
        return QString();
    }

    // 检查是否是chunked编码
    if (headers.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
        body = decodeChunkedResponse(body);
    }

    // 检查是否是gzip压缩
    if (headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive)) {
        body = decompressGzipResponse(body);
    }

    return body;
}

// 提取Content-Length头
int UltraFastTLS::extractContentLength(const QString& headers)
{
    QStringList headerLines = headers.split("\r\n");
    for (const QString& line : headerLines) {
        if (line.startsWith("Content-Length:", Qt::CaseInsensitive)) {
            QString lengthStr = line.mid(15).trimmed();
            bool ok;
            int length = lengthStr.toInt(&ok);
            return ok ? length : -1;
        }
    }
    return -1;
}

// 解码chunked传输编码
QString UltraFastTLS::decodeChunkedResponse(const QString& chunkedData)
{
    QByteArray data = chunkedData.toUtf8();
    QByteArray result;
    int pos = 0;

    while (pos < data.size()) {
        // 查找chunk大小行的结束
        int crlfPos = data.indexOf("\r\n", pos);
        if (crlfPos == -1) break;

        // 解析chunk大小（十六进制）
        QString chunkSizeStr = QString::fromUtf8(data.mid(pos, crlfPos - pos));
        bool ok;
        int chunkSize = chunkSizeStr.toInt(&ok, 16);
        if (!ok) {
            break;
        }

        if (chunkSize == 0) {
            // 最后一个chunk
            break;
        }

        // 跳过CRLF，读取chunk数据
        pos = crlfPos + 2;
        if (pos + chunkSize > data.size()) {
            break;
        }

        result.append(data.mid(pos, chunkSize));
        pos += chunkSize + 2; // 跳过chunk数据和结尾的CRLF
    }

    return QString::fromUtf8(result);
}

// 解压gzip响应
QString UltraFastTLS::decompressGzipResponse(const QString& gzipData)
{
    return CompressionHelper::decompressGzip(gzipData);
}

// SSL错误描述函数
QString UltraFastTLS::getSSLErrorDescription(int sslError)
{
    switch (sslError) {
        case SSL_ERROR_NONE:
            return "无错误";
        case SSL_ERROR_SSL:
            return "SSL协议错误 - 可能是证书问题或协议不匹配";
        case SSL_ERROR_WANT_READ:
            return "需要更多数据读取";
        case SSL_ERROR_WANT_WRITE:
            return "需要更多数据写入";
        case SSL_ERROR_WANT_X509_LOOKUP:
            return "X509证书查找错误";
        case SSL_ERROR_SYSCALL:
            return "系统调用错误 - 可能是网络中断";
        case SSL_ERROR_ZERO_RETURN:
            return "连接被对方正常关闭";
        case SSL_ERROR_WANT_CONNECT:
            return "连接未完成";
        case SSL_ERROR_WANT_ACCEPT:
            return "接受未完成";
        default:
            return QString("未知SSL错误 (%1)").arg(sslError);
    }
}

// 检查连接健康状态 (已删除 - 永不重连实验)
bool UltraFastTLS::isConnectionHealthy(ConnectionInfo* /*conn*/)
{
    // 🗑️ 健康检查已删除，总是返回true
    return true; // 假装所有连接都健康
}

// 智能重试机制
void UltraFastTLS::cleanupStaleConnections()
{
    // 连接清理已删除，保持所有连接
}

QString UltraFastTLS::handleSmartRetry(const ParsedUrlInfo& /*parsedUrl*/, const QString& /*httpRequest*/)
{
    // 智能重试已删除
    return QString();
}

// 分析连接断开原因
QString UltraFastTLS::analyzeDisconnectReason(ConnectionInfo* conn)
{
    if (!conn) {
        return "连接信息为空";
    }

    auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - conn->lastUsed).count();

    QString reason = "服务器主动断开SSL连接，原因分析：";

    // 分析断开模式
    if (conn->reuseCount == 1) {
        reason += QString("首次使用就断开，可能是服务器拒绝连接复用");
    } else if (conn->reuseCount >= 100) {
        reason += QString("第%1次复用被拒绝，服务器有复用次数限制").arg(conn->reuseCount);
    } else if (conn->reuseCount == 2) {
        reason += QString("第2次复用失败，可能是网络波动或服务器负载");
    } else {
        reason += QString("第%1次复用失败，异常断开").arg(conn->reuseCount);
    }

    // 分析时间因素
    if (connectionAge > 30) {
        reason += QString("，连接已存活%1秒，可能超时").arg(connectionAge);
    } else if (connectionAge < 1) {
        reason += QString("，连接刚建立%1秒就断开，服务器快速拒绝").arg(connectionAge);
    }

    // 分析Keep-Alive声明
    if (conn->serverSaidKeepAlive) {
        reason += "，服务器之前声明支持keep-alive但实际断开（虚假keep-alive）";
    } else {
        reason += "，服务器未声明keep-alive支持";
    }

    return reason;
}

// 获取TCP连接状态
QString UltraFastTLS::getTcpConnectionState(ConnectionInfo* conn)
{
    if (!conn) {
        return "连接信息为空";
    }

    if (conn->socket == INVALID_SOCKET) {
        return "Socket无效(已关闭)";
    }

    // 简单的连接状态检测
    QString stateStr;

    if (conn->serverClosed) {
        stateStr = "服务器已关闭连接";
    } else if (conn->isValid) {
        stateStr = "连接有效";
    } else {
        stateStr = "连接已标记为无效";
    }

    // 添加连接信息
    stateStr += QString(" (复用%1次)").arg(conn->reuseCount);

    if (conn->serverSaidKeepAlive) {
        stateStr += " [声明Keep-Alive]";
    }

    return stateStr;
}

// 检测服务器SSL复连支持能力 (已删除 - 永不重连实验)
QString UltraFastTLS::detectSSLReconnectSupport(ConnectionInfo* /*conn*/)
{
    // 🗑️ SSL检测已删除，直接返回简单信息
    return "🗑️ SSL检测已删除 - 永不重连实验";
}

// 代理支持 - 高性能版本
void UltraFastTLS::setProxy(const QString& host, int port, const QString& type,
                           const QString& user, const QString& pass)
{
    QMutexLocker locker(&m_poolMutex);

    // 检查代理配置是否真的发生了变化
    bool configChanged = (m_proxyConfig.host != host ||
                         m_proxyConfig.port != port ||
                         m_proxyConfig.type != type.toLower() ||
                         m_proxyConfig.user != user ||
                         m_proxyConfig.pass != pass);

    if (!configChanged) {
        // 配置没有变化，直接返回，避免不必要的连接池清理
        return;
    }

    m_proxyConfig.host = host;
    m_proxyConfig.port = port;
    m_proxyConfig.type = type.toLower();
    m_proxyConfig.user = user;
    m_proxyConfig.pass = pass;
    m_proxyConfig.enabled = !host.isEmpty() && port > 0;

    if (!m_quietMode) {
        if (m_proxyConfig.enabled) {
            emit debugLog(QString("🔗 UltraFastTLS代理已设置: %1:%2 (%3)")
                         .arg(host).arg(port).arg(type));
        } else {
            emit debugLog("🔗 UltraFastTLS代理已禁用");
        }
    }

    // 代理配置变更时清理连接池（高性能：只清理当前实例的连接）
    cleanupAllConnections();
}

void UltraFastTLS::clearProxy()
{
    QMutexLocker locker(&m_poolMutex);

    m_proxyConfig.enabled = false;
    m_proxyConfig.host.clear();
    m_proxyConfig.port = 0;
    m_proxyConfig.user.clear();
    m_proxyConfig.pass.clear();

    if (!m_quietMode) {
        emit debugLog("🔗 UltraFastTLS代理配置已清除");
    }

    // 清理现有连接
    cleanupAllConnections();
}

bool UltraFastTLS::hasProxy() const
{
    QMutexLocker locker(&m_poolMutex);
    return m_proxyConfig.enabled;
}

// 从HTTP请求中提取POST数据
QString UltraFastTLS::extractPostDataFromRequest(const QString& httpRequest)
{
    // 查找HTTP头部和正文的分隔符
    int separatorPos = httpRequest.indexOf("\r\n\r\n");
    if (separatorPos == -1) {
        return QString();
    }

    // 返回正文部分
    return httpRequest.mid(separatorPos + 4);
}

