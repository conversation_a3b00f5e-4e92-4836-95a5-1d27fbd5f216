#ifndef GZIP_DECOMPRESSOR_H
#define GZIP_DECOMPRESSOR_H

#include <QByteArray>
#include <QString>

class GzipDecompressor
{
public:
    // 使用多种方法尝试解压gzip数据
    static QString decompress(const QByteArray& compressedData);
    
private:
    // 方法1：使用Windows API
    static QString decompressWithWindowsAPI(const QByteArray& data);
    
    // 方法2：使用预编译的zlib库
    static QString decompressWithZlib(const QByteArray& data);
    
    // 方法3：使用Qt + 手动头部解析
    static QString decompressWithQt(const QByteArray& data);
    
    // 方法4：使用miniz库（单头文件实现）
    static QString decompressWithMiniz(const QByteArray& data);
    
    // 辅助函数：解析gzip头部
    static int parseGzipHeader(const QByteArray& data);
    
    // 辅助函数：验证gzip格式
    static bool isGzipFormat(const QByteArray& data);
};

#endif // GZIP_DECOMPRESSOR_H
