# PowerShell脚本：下载和设置zlib库
Write-Host "🔧 开始设置zlib库..." -ForegroundColor Green

# 创建zlib_lib目录
$zlibDir = "zlib_lib"
if (!(Test-Path $zlibDir)) {
    New-Item -ItemType Directory -Path $zlibDir
    Write-Host "📁 创建目录: $zlibDir" -ForegroundColor Yellow
}

# 下载zlib预编译库 (Windows MinGW版本)
$zlibUrl = "https://github.com/madler/zlib/archive/refs/tags/v1.3.1.zip"
$zipFile = "zlib-1.3.1.zip"

Write-Host "📥 下载zlib源码..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $zlibUrl -OutFile $zipFile
    Write-Host "✅ 下载完成" -ForegroundColor Green
} catch {
    Write-Host "❌ 下载失败: $_" -ForegroundColor Red
    exit 1
}

# 解压文件
Write-Host "📦 解压zlib..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath "." -Force
    Write-Host "✅ 解压完成" -ForegroundColor Green
} catch {
    Write-Host "❌ 解压失败: $_" -ForegroundColor Red
    exit 1
}

# 移动文件到正确位置
$sourceDir = "zlib-1.3.1"
if (Test-Path $sourceDir) {
    # 复制头文件
    $includeDir = "$zlibDir\include"
    if (!(Test-Path $includeDir)) {
        New-Item -ItemType Directory -Path $includeDir
    }
    
    Copy-Item "$sourceDir\zlib.h" "$includeDir\" -Force
    Copy-Item "$sourceDir\zconf.h" "$includeDir\" -Force
    
    # 创建lib目录（稍后需要编译）
    $libDir = "$zlibDir\lib"
    if (!(Test-Path $libDir)) {
        New-Item -ItemType Directory -Path $libDir
    }
    
    Write-Host "✅ 头文件已复制到 $includeDir" -ForegroundColor Green
    Write-Host "⚠️  需要编译库文件，请运行以下命令:" -ForegroundColor Yellow
    Write-Host "   cd $sourceDir" -ForegroundColor Cyan
    Write-Host "   mingw32-make -f win32/Makefile.gcc" -ForegroundColor Cyan
    Write-Host "   copy libz.a ..\$libDir\zlib.lib" -ForegroundColor Cyan
} else {
    Write-Host "❌ 未找到解压后的源码目录" -ForegroundColor Red
    exit 1
}

# 清理临时文件
Remove-Item $zipFile -Force
Write-Host "🧹 清理临时文件" -ForegroundColor Yellow

Write-Host "🎉 zlib设置完成！" -ForegroundColor Green
Write-Host "📍 头文件位置: $includeDir" -ForegroundColor Cyan
Write-Host "📍 库文件位置: $libDir (需要编译)" -ForegroundColor Cyan
