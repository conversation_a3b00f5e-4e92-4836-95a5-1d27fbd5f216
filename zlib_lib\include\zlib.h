/* zlib.h -- interface of the 'zlib' general purpose compression library
  version 1.3.1, January 22nd, 2024

  Copyright (C) 1995-2024 <PERSON><PERSON><PERSON><PERSON> and <PERSON>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.

  <PERSON><PERSON><PERSON><PERSON> Gail<PERSON>        Mark <PERSON>
  <EMAIL>          <EMAIL>
*/

#ifndef ZLIB_H
#define ZLIB_H

#include "zconf.h"

#ifdef __cplusplus
extern "C" {
#endif

#define ZLIB_VERSION "1.3.1"
#define ZLIB_VERNUM 0x1310
#define ZLIB_VER_MAJOR 1
#define ZLIB_VER_MINOR 3
#define ZLIB_VER_REVISION 1
#define ZLIB_VER_SUBREVISION 0

/* compression levels */
#define Z_NO_COMPRESSION         0
#define Z_BEST_SPEED             1
#define Z_BEST_COMPRESSION       9
#define Z_DEFAULT_COMPRESSION  (-1)

/* compression strategy */
#define Z_FILTERED            1
#define Z_HUFFMAN_ONLY        2
#define Z_RLE                 3
#define Z_FIXED               4
#define Z_DEFAULT_STRATEGY    0

/* Possible values of the data_type field (though see inflate()) */
#define Z_BINARY   0
#define Z_TEXT     1
#define Z_ASCII    Z_TEXT   /* for compatibility with 1.2.2 and earlier */
#define Z_UNKNOWN  2

/* The deflate compression method (the only one supported in this version) */
#define Z_DEFLATED   8

/* for initializing zalloc, zfree, opaque */
#define Z_NULL  0

#define MAX_WBITS   15 /* 32K LZ77 window */

/* Return codes for the compression/decompression functions. Negative values
 * are errors, positive values are used for special but normal events.
 */
#define Z_OK            0
#define Z_STREAM_END    1
#define Z_NEED_DICT     2
#define Z_ERRNO        (-1)
#define Z_STREAM_ERROR (-2)
#define Z_DATA_ERROR   (-3)
#define Z_MEM_ERROR    (-4)
#define Z_BUF_ERROR    (-5)
#define Z_VERSION_ERROR (-6)

/* compression levels */
#define Z_NO_FLUSH      0
#define Z_PARTIAL_FLUSH 1
#define Z_SYNC_FLUSH    2
#define Z_FULL_FLUSH    3
#define Z_FINISH        4
#define Z_BLOCK         5
#define Z_TREES         6

typedef void* (*alloc_func)(void* opaque, unsigned items, unsigned size);
typedef void (*free_func)(void* opaque, void* address);

struct internal_state;

typedef struct z_stream_s {
    unsigned char* next_in;     /* next input byte */
    unsigned int avail_in;      /* number of bytes available at next_in */
    unsigned long total_in;     /* total number of input bytes read so far */

    unsigned char* next_out;    /* next output byte will go here */
    unsigned int avail_out;     /* remaining free space at next_out */
    unsigned long total_out;    /* total number of bytes output so far */

    char* msg;                  /* last error message, NULL if no error */
    struct internal_state* state; /* not visible by applications */

    alloc_func zalloc;          /* used to allocate the internal state */
    free_func zfree;            /* used to free the internal state */
    void* opaque;               /* private data object passed to zalloc and zfree */

    int data_type;              /* best guess about the data type: binary or text
                                   for deflate, or the decoding state for inflate */
    unsigned long adler;        /* Adler-32 or CRC-32 value of the uncompressed data */
    unsigned long reserved;     /* reserved for future use */
} z_stream;

typedef z_stream* z_streamp;
typedef unsigned char Byte;
typedef Byte* Bytef;
typedef unsigned int uInt;
typedef unsigned long uLong;

/* basic functions */
extern int inflateInit2(z_streamp strm, int windowBits);
extern int inflate(z_streamp strm, int flush);
extern int inflateEnd(z_streamp strm);

#ifdef __cplusplus
}
#endif

#endif /* ZLIB_H */
