#include "gzip_decompressor.h"
#include <QDebug>

// 移除Windows API，专注于zlib实现

// 暂时禁用外部zlib，避免类型冲突，使用Qt内置解压
#define HAS_ZLIB 0

QString GzipDecompressor::decompress(const QByteArray& compressedData)
{
    if (!isGzipFormat(compressedData)) {
        return QString::fromUtf8(compressedData);
    }
    
    qDebug() << "🗜️ 开始gzip解压，原始大小:" << compressedData.size() << "字节";

    // 方法1：优先尝试外部zlib库（最可靠）
    QString result = decompressWithZlib(compressedData);
    if (!result.isEmpty() && result != QString::fromUtf8(compressedData)) {
        qDebug() << "✅ 外部zlib解压成功，大小:" << result.size() << "字符";
        return result;
    }

    // 方法2：尝试Windows API
    result = decompressWithWindowsAPI(compressedData);
    if (!result.isEmpty() && result != QString::fromUtf8(compressedData)) {
        qDebug() << "✅ Windows API解压成功，大小:" << result.size() << "字符";
        return result;
    }
    
    // 方法3：尝试Qt + 手动解析
    result = decompressWithQt(compressedData);
    if (!result.isEmpty() && result != QString::fromUtf8(compressedData)) {
        qDebug() << "✅ Qt解压成功，大小:" << result.size() << "字符";
        return result;
    }
    
    // 方法4：尝试miniz
    result = decompressWithMiniz(compressedData);
    if (!result.isEmpty() && result != QString::fromUtf8(compressedData)) {
        qDebug() << "✅ miniz解压成功，大小:" << result.size() << "字符";
        return result;
    }
    
    qDebug() << "❌ 所有解压方法都失败，返回原始数据";
    return QString::fromUtf8(compressedData);
}

bool GzipDecompressor::isGzipFormat(const QByteArray& data)
{
    return data.size() >= 10 &&
           (unsigned char)data[0] == 0x1f &&
           (unsigned char)data[1] == 0x8b;
}

int GzipDecompressor::parseGzipHeader(const QByteArray& data)
{
    if (!isGzipFormat(data)) return -1;
    
    int headerSize = 10; // 基本头部大小
    const unsigned char* bytes = (const unsigned char*)data.constData();
    
    // 检查标志位
    unsigned char flags = bytes[3];
    
    // 跳过额外字段
    if (flags & 0x04) { // FEXTRA
        if (data.size() < headerSize + 2) return -1;
        int extraLen = bytes[headerSize] | (bytes[headerSize + 1] << 8);
        headerSize += 2 + extraLen;
    }
    
    // 跳过文件名
    if (flags & 0x08) { // FNAME
        while (headerSize < data.size() && bytes[headerSize] != 0) {
            headerSize++;
        }
        headerSize++; // 跳过null终止符
    }
    
    // 跳过注释
    if (flags & 0x10) { // FCOMMENT
        while (headerSize < data.size() && bytes[headerSize] != 0) {
            headerSize++;
        }
        headerSize++; // 跳过null终止符
    }
    
    // 跳过CRC16
    if (flags & 0x02) { // FHCRC
        headerSize += 2;
    }
    
    return headerSize;
}

QString GzipDecompressor::decompressWithWindowsAPI(const QByteArray& data)
{
    // Windows API暂时不实现，专注于zlib
    Q_UNUSED(data)
    return QString();
}

QString GzipDecompressor::decompressWithZlib(const QByteArray& data)
{
#if HAS_ZLIB
    // 使用外部zlib库进行gzip解压
    if (!isGzipFormat(data)) {
        return QString::fromUtf8(data);
    }

    // 初始化zlib流
    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = data.size();
    stream.next_in = const_cast<Bytef*>(reinterpret_cast<const Bytef*>(data.constData()));

    // 初始化gzip解压 (16 + MAX_WBITS 表示gzip格式)
    if (inflateInit2(&stream, 16 + MAX_WBITS) != Z_OK) {
        qDebug() << "❌ zlib初始化失败";
        return QString::fromUtf8(data);
    }

    // 预分配输出缓冲区
    QByteArray decompressed;
    decompressed.resize(data.size() * 4); // 预估4倍大小

    stream.avail_out = decompressed.size();
    stream.next_out = (Bytef*)decompressed.data();

    // 执行解压
    int ret = inflate(&stream, Z_FINISH);

    if (ret == Z_STREAM_END) {
        // 解压成功，调整大小
        decompressed.resize(decompressed.size() - stream.avail_out);
        inflateEnd(&stream);
        qDebug() << "✅ zlib解压成功，原始:" << data.size() << "字节，解压后:" << decompressed.size() << "字节";
        return QString::fromUtf8(decompressed);
    } else {
        // 解压失败
        qDebug() << "❌ zlib解压失败，错误代码:" << ret;
        inflateEnd(&stream);
        return QString::fromUtf8(data);
    }
#else
    Q_UNUSED(data)
    qDebug() << "❌ zlib不可用";
    return QString();
#endif
}

QString GzipDecompressor::decompressWithQt(const QByteArray& data)
{
    int headerSize = parseGzipHeader(data);
    if (headerSize < 0 || headerSize + 8 >= data.size()) {
        return QString::fromUtf8(data);
    }
    
    // 提取deflate数据
    QByteArray deflateData = data.mid(headerSize, data.size() - headerSize - 8);
    
    // 使用Qt的qUncompress
    QByteArray result = qUncompress(deflateData);
    
    if (!result.isEmpty()) {
        return QString::fromUtf8(result);
    }
    
    return QString::fromUtf8(data);
}

QString GzipDecompressor::decompressWithMiniz(const QByteArray& data)
{
    // 简化的deflate解压实现
    // 这里使用Qt的qUncompress作为后备方案
    int headerSize = parseGzipHeader(data);
    if (headerSize < 0 || headerSize + 8 >= data.size()) {
        return QString::fromUtf8(data);
    }

    // 提取deflate数据
    QByteArray deflateData = data.mid(headerSize, data.size() - headerSize - 8);

    // 尝试多种deflate解压方式
    QByteArray result;

    // 方式1：直接使用qUncompress
    result = qUncompress(deflateData);
    if (!result.isEmpty()) {
        return QString::fromUtf8(result);
    }

    // 方式2：添加zlib头部后使用qUncompress
    QByteArray zlibData;
    zlibData.append(char(0x78)); // CMF
    zlibData.append(char(0x9C)); // FLG
    zlibData.append(deflateData);

    result = qUncompress(zlibData);
    if (!result.isEmpty()) {
        return QString::fromUtf8(result);
    }

    return QString::fromUtf8(data);
}
